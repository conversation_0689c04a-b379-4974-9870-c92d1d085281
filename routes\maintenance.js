const express = require('express');
const router = express.Router();
const mysql = require('mysql2');

// دالة للتحقق من صحة التوكن
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'غير مصرح' });
  }

  const jwt = require('jsonwebtoken');
  jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key', (err, user) => {
    if (err) {
      return res.status(403).json({ error: 'توكن غير صالح' });
    }
    req.user = user;
    next();
  });
};

// اختبار الاتصال بقاعدة البيانات
router.post("/test-connection", async (req, res) => {
  const { host, port, database, username, password } = req.body;

  if (!host || !database || !username || !password) {
    return res.status(400).json({ 
      success: false, 
      message: 'جميع الحقول مطلوبة' 
    });
  }

  const testConfig = {
    host: host,
    port: parseInt(port) || 3306,
    user: username,
    password: password,
    database: database,
    connectTimeout: 10000,
    acquireTimeout: 10000,
    timeout: 10000
  };

  let testConnection;
  try {
    testConnection = mysql.createConnection(testConfig);
    
    await new Promise((resolve, reject) => {
      testConnection.connect((err) => {
        if (err) {
          reject(err);
        } else {
          resolve();
        }
      });
    });

    res.json({
      success: true,
      message: 'اتصال ناجح'
    });
  } catch (error) {
    console.error('خطأ في الاتصال بقاعدة البيانات:', error);
    res.status(500).json({ 
      success: false, 
      message: 'فشل في الاتصال بقاعدة البيانات: ' + error.message 
    });
  } finally {
    if (testConnection) {
      testConnection.end();
    }
  }
});

// تحديث إعدادات قاعدة البيانات
router.post("/update-db-config", async (req, res) => {
  const { host, port, database, username, password } = req.body;

  if (!host || !database || !username || !password) {
    return res.status(400).json({ 
      success: false, 
      message: 'جميع الحقول مطلوبة' 
    });
  }

  try {
    const pool = req.app.locals.pool;
    
    // إغلاق الاتصال القديم
    if (pool) {
      pool.end();
    }

    // تحديث الاتصال
    const newDbConfig = {
      host: host,
      user: username,
      password: password,
      database: database,
      port: parseInt(port) || 3306,
      waitForConnections: true,
      connectionLimit: 10,
      queueLimit: 0
    };

    const newPool = mysql.createPool(newDbConfig);
    req.app.locals.pool = newPool;

    // اختبار الاتصال الجديد
    await newPool.promise().query('SELECT 1');

    res.json({ 
      success: true, 
      message: 'تم تحديث إعدادات قاعدة البيانات بنجاح' 
    });
  } catch (error) {
    console.error('خطأ في تحديث إعدادات قاعدة البيانات:', error);
    res.status(500).json({ 
      success: false, 
      message: 'فشل في تحديث إعدادات قاعدة البيانات: ' + error.message 
    });
  }
});

// إنشاء جدول المساهمات
router.get("/create-contributions-table", async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    const createTableQuery = `
      CREATE TABLE IF NOT EXISTS contributions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        employee_code VARCHAR(50) NOT NULL,
        employee_name VARCHAR(255) NOT NULL,
        department VARCHAR(255) NOT NULL,
        contribution_type ENUM('تأمين اجتماعي', 'تأمين صحي', 'صندوق ادخار', 'أخرى') NOT NULL,
        amount DECIMAL(10,2) NOT NULL,
        contribution_date DATE NOT NULL,
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_employee_code (employee_code),
        INDEX idx_contribution_type (contribution_type),
        INDEX idx_contribution_date (contribution_date),
        INDEX idx_created_at (created_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول المساهمات'
    `;

    await pool.promise().query(createTableQuery);

    // التحقق من وجود بيانات تجريبية
    const [rows] = await pool.promise().query('SELECT COUNT(*) as count FROM contributions');
    
    if (rows[0].count === 0) {
      // إضافة بيانات تجريبية
      const sampleData = [
        ['12345', 'أحمد محمد علي', 'الموارد البشرية', 'تأمين اجتماعي', 500.00, '2024-01-15', 'مساهمة شهرية'],
        ['12346', 'فاطمة أحمد', 'المحاسبة', 'تأمين صحي', 300.00, '2024-01-15', 'مساهمة شهرية'],
        ['12347', 'محمد علي', 'تقنية المعلومات', 'صندوق ادخار', 200.00, '2024-01-15', 'مساهمة اختيارية']
      ];

      for (const data of sampleData) {
        await pool.promise().query(
          'INSERT INTO contributions (employee_code, employee_name, department, contribution_type, amount, contribution_date, notes) VALUES (?, ?, ?, ?, ?, ?, ?)',
          data
        );
      }
    }

    res.json({ 
      message: 'تم إنشاء جدول المساهمات بنجاح',
      table_created: true
    });
  } catch (error) {
    console.error('خطأ في إنشاء جدول المساهمات:', error);
    res.status(500).json({ 
      error: 'فشل في إنشاء جدول المساهمات: ' + error.message 
    });
  }
});

// إعداد جدول الإجازات
router.get('/setup-vacations-table', async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    const createTableSQL = `
      CREATE TABLE IF NOT EXISTS vacations (
        id INT AUTO_INCREMENT PRIMARY KEY,
        employee_code VARCHAR(50) NOT NULL,
        employee_name VARCHAR(255) NOT NULL,
        department VARCHAR(255) NOT NULL,
        vacation_type ENUM('annual', 'sick', 'emergency', 'maternity', 'official') NOT NULL,
        vacation_date DATE NOT NULL,
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_employee_code (employee_code),
        INDEX idx_vacation_type (vacation_type),
        INDEX idx_vacation_date (vacation_date),
        INDEX idx_created_at (created_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول الإجازات'
    `;

    await pool.promise().query(createTableSQL);

    // التحقق من وجود بيانات تجريبية
    const [rows] = await pool.promise().query('SELECT COUNT(*) as count FROM vacations');
    
    if (rows[0].count === 0) {
      // إضافة بيانات تجريبية
      const sampleData = [
        ['12345', 'أحمد محمد علي', 'الموارد البشرية', 'annual', '2024-01-20', 'إجازة سنوية'],
        ['12346', 'فاطمة أحمد', 'المحاسبة', 'sick', '2024-01-22', 'إجازة مرضية'],
        ['12347', 'محمد علي', 'تقنية المعلومات', 'emergency', '2024-01-25', 'إجازة طارئة']
      ];

      for (const data of sampleData) {
        await pool.promise().query(
          'INSERT INTO vacations (employee_code, employee_name, department, vacation_type, vacation_date, notes) VALUES (?, ?, ?, ?, ?, ?)',
          data
        );
      }
    }

    res.json({ 
      message: 'تم إعداد جدول الإجازات بنجاح',
      table_created: true
    });
  } catch (error) {
    console.error('خطأ في إعداد جدول الإجازات:', error);
    res.status(500).json({ 
      error: 'فشل في إعداد جدول الإجازات: ' + error.message 
    });
  }
});

// حساب رصيد الإجازات لجميع الموظفين
router.post('/calculate-leave-balance', (req, res) => {
  const pool = req.app.locals.pool;
  
  pool.getConnection((err, connection) => {
    if (err) {
      console.error("Error connecting to database:", err);
      return res.status(500).json({ error: "خطأ في الاتصال بقاعدة البيانات" });
    }

    connection.query("SELECT * FROM employees", (err, employees) => {
      if (err) {
        console.error("Error fetching employees:", err);
        connection.release();
        return res.status(500).json({ error: "خطأ في جلب بيانات الموظفين" });
      }

      let updatedCount = 0;

      // تحديث رصيد الإجازات لكل موظف
      const updatePromises = employees.map(employee => {
        return new Promise((resolve, reject) => {
          // حساب رصيد الإجازات بناءً على تاريخ التعيين
          let calculatedBalance = 7; // قيمة افتراضية

          if (employee.hire_date) {
            try {
              let hireDate = new Date(employee.hire_date);

              // معالجة التواريخ بصيغ مختلفة
              if (typeof employee.hire_date === 'string' && employee.hire_date.includes('/')) {
                const parts = employee.hire_date.split('/');
                if (parts.length === 3) {
                  hireDate = new Date(`${parts[2]}-${parts[1]}-${parts[0]}`);
                }
              }

              const currentDate = new Date();
              const currentYear = currentDate.getFullYear();
              const referenceDate = new Date(currentYear, 5, 25); // 25 يونيو

              if (currentDate < referenceDate) {
                referenceDate.setFullYear(currentYear - 1);
              }

              if (!isNaN(hireDate.getTime()) && hireDate <= currentDate) {
                const yearDiff = referenceDate.getFullYear() - hireDate.getFullYear();
                const monthDiff = referenceDate.getMonth() - hireDate.getMonth();
                const dayDiff = referenceDate.getDate() - hireDate.getDate();

                let yearsOfService = yearDiff;
                if (monthDiff < 0 || (monthDiff === 0 && dayDiff < 0)) {
                  yearsOfService--;
                }

                const totalMonths = yearDiff * 12 + monthDiff;
                const monthsOfService = totalMonths >= 0 ? totalMonths : 0;

                if (monthsOfService < 6) {
                  calculatedBalance = 7;
                } else if (yearsOfService < 10) {
                  calculatedBalance = 21;
                } else {
                  calculatedBalance = 30;
                }
              }
            } catch (error) {
              console.error('خطأ في حساب رصيد الإجازات:', error);
            }
          }

          // تحديث رصيد الإجازات في قاعدة البيانات
          connection.query(
            "UPDATE employees SET leave_balance = ? WHERE code = ?",
            [calculatedBalance, employee.code],
            (err, result) => {
              if (err) {
                console.error(`خطأ في تحديث رصيد الإجازات للموظف ${employee.code}:`, err);
                reject(err);
              } else {
                if (result.affectedRows > 0) {
                  updatedCount++;
                }
                resolve();
              }
            }
          );
        });
      });

      Promise.all(updatePromises)
        .then(() => {
          connection.release();
          res.json({
            message: `تم تحديث رصيد الإجازات لـ ${updatedCount} موظف من أصل ${employees.length}`,
            updated_count: updatedCount,
            total_employees: employees.length
          });
        })
        .catch((error) => {
          connection.release();
          console.error("خطأ في تحديث رصيد الإجازات:", error);
          res.status(500).json({ error: "خطأ في تحديث رصيد الإجازات" });
        });
    });
  });
});

// نقطة نهاية لتشغيل السيرفر
router.post('/start', (req, res) => {
  res.json({ message: 'Server started successfully' });
});

// نقطة نهاية لإيقاف السيرفر
router.post('/stop', (req, res) => {
  res.json({ message: 'Server stopped successfully' });
});

module.exports = router;
