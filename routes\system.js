const express = require("express");
const fs = require("fs");
const path = require("path");
const { pool, testConnection, updateConnection, getCurrentConfig } = require("../config/database");
const { authenticateToken, checkPermission } = require("../middleware/auth");

const router = express.Router();

// الحصول على حالة الخادم
router.get('/status', authenticateToken, async (req, res) => {
  try {
    // اختبار الاتصال بقاعدة البيانات
    const dbStatus = await testConnection();
    
    res.json({
      server: 'running',
      database: dbStatus ? 'connected' : 'disconnected',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      version: process.version
    });
  } catch (error) {
    console.error('خطأ في فحص حالة الخادم:', error);
    res.status(500).json({ 
      server: 'running',
      database: 'error',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// اختبار اتصال قاعدة البيانات
router.post('/test-connection', authenticateToken, checkPermission('manage_users'), async (req, res) => {
  try {
    const { host, user, password, database, port } = req.body;
    
    if (!host || !user || !password || !database) {
      return res.status(400).json({ error: 'جميع بيانات الاتصال مطلوبة' });
    }
    
    // اختبار الاتصال بالإعدادات الجديدة
    const mysql = require('mysql2');
    const testPool = mysql.createPool({
      host: host,
      user: user,
      password: password,
      database: database,
      port: port || 3306,
      waitForConnections: true,
      connectionLimit: 10,
      queueLimit: 0
    });
    
    // محاولة تنفيذ استعلام بسيط
    await testPool.promise().query('SELECT 1');
    
    // إغلاق الاتصال التجريبي
    testPool.end();
    
    res.json({
      success: true,
      message: 'اتصال ناجح'
    });
  } catch (error) {
    console.error('خطأ في اختبار الاتصال:', error);
    res.status(500).json({ 
      success: false, 
      error: 'فشل في الاتصال بقاعدة البيانات: ' + error.message 
    });
  }
});

// تحديث إعدادات قاعدة البيانات
router.post('/change-connection', authenticateToken, checkPermission('manage_users'), async (req, res) => {
  try {
    const { host, user, password, database, port } = req.body;
    
    if (!host || !user || !password || !database) {
      return res.status(400).json({ error: 'جميع بيانات الاتصال مطلوبة' });
    }
    
    // تحديث الاتصال
    const success = await updateConnection({ host, user, password, database, port: port || 3306 });
    
    if (success) {
      res.json({ 
        success: true, 
        message: 'تم تحديث إعدادات قاعدة البيانات بنجاح' 
      });
    } else {
      res.status(500).json({ 
        success: false, 
        error: 'فشل في تحديث إعدادات قاعدة البيانات' 
      });
    }
  } catch (error) {
    console.error('خطأ في تحديث إعدادات قاعدة البيانات:', error);
    res.status(500).json({ 
      success: false, 
      error: 'فشل في تحديث إعدادات قاعدة البيانات: ' + error.message 
    });
  }
});

// حفظ الإعدادات في ملف .env
router.post('/save-settings', authenticateToken, checkPermission('manage_users'), async (req, res) => {
  try {
    const { host, user, password, database, port, serverPort } = req.body;
    
    if (!host || !user || !password || !database) {
      return res.status(400).json({ error: 'جميع بيانات الاتصال مطلوبة' });
    }
    
    // إنشاء محتوى ملف .env
    const envContent = `# إعدادات قاعدة البيانات
DB_HOST=${host}
DB_USER=${user}
DB_PASSWORD=${password}
DB_NAME=${database}
DB_PORT=${port || 3306}

# إعدادات الخادم
PORT=${serverPort || 3000}
JWT_SECRET=your-secret-key

# إعدادات أخرى
NODE_ENV=production
`;
    
    // كتابة الملف
    const envPath = path.join(process.cwd(), '.env');
    fs.writeFileSync(envPath, envContent, 'utf8');
    
    res.json({ 
      success: true, 
      message: 'تم حفظ الإعدادات في ملف .env بنجاح' 
    });
  } catch (error) {
    console.error('خطأ في حفظ الإعدادات:', error);
    res.status(500).json({ 
      success: false, 
      error: 'فشل في حفظ الإعدادات: ' + error.message 
    });
  }
});

// إعادة تشغيل الخادم
router.post('/restart-server', authenticateToken, checkPermission('manage_users'), async (req, res) => {
  try {
    res.json({ 
      success: true, 
      message: 'سيتم إعادة تشغيل الخادم خلال 3 ثوانٍ...' 
    });
    
    // إعادة تشغيل الخادم بعد 3 ثوانٍ
    setTimeout(() => {
      console.log('إعادة تشغيل الخادم...');
      process.exit(0);
    }, 3000);
  } catch (error) {
    console.error('خطأ في إعادة تشغيل الخادم:', error);
    res.status(500).json({ 
      success: false, 
      error: 'فشل في إعادة تشغيل الخادم: ' + error.message 
    });
  }
});

// الحصول على الإعدادات الحالية
router.get('/current-settings', authenticateToken, checkPermission('manage_users'), async (req, res) => {
  try {
    const config = getCurrentConfig();
    
    // إخفاء كلمة المرور لأسباب أمنية
    const safeConfig = {
      host: config.host,
      user: config.user,
      database: config.database,
      port: config.port,
      serverPort: process.env.PORT || 3000
    };
    
    res.json(safeConfig);
  } catch (error) {
    console.error('خطأ في جلب الإعدادات الحالية:', error);
    res.status(500).json({ 
      error: 'فشل في جلب الإعدادات الحالية: ' + error.message 
    });
  }
});

// تحديث اتصال قاعدة البيانات
router.post('/update-connection', authenticateToken, checkPermission('manage_users'), async (req, res) => {
  try {
    const { host, user, password, database, port } = req.body;
    
    if (!host || !user || !password || !database) {
      return res.status(400).json({ error: 'جميع بيانات الاتصال مطلوبة' });
    }
    
    // تحديث الاتصال
    const success = await updateConnection({ host, user, password, database, port: port || 3306 });
    
    if (success) {
      res.json({ 
        success: true, 
        message: 'تم تحديث اتصال قاعدة البيانات بنجاح' 
      });
    } else {
      res.status(500).json({ 
        success: false, 
        error: 'فشل في تحديث اتصال قاعدة البيانات' 
      });
    }
  } catch (error) {
    console.error('خطأ في تحديث اتصال قاعدة البيانات:', error);
    res.status(500).json({ 
      success: false, 
      error: 'فشل في تحديث اتصال قاعدة البيانات: ' + error.message 
    });
  }
});

// الحصول على معلومات النظام
router.get('/info', authenticateToken, async (req, res) => {
  try {
    const os = require('os');
    
    res.json({
      system: {
        platform: os.platform(),
        arch: os.arch(),
        release: os.release(),
        hostname: os.hostname(),
        uptime: os.uptime()
      },
      node: {
        version: process.version,
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        cpu: process.cpuUsage()
      },
      database: {
        connected: await testConnection(),
        config: {
          host: getCurrentConfig().host,
          database: getCurrentConfig().database,
          port: getCurrentConfig().port
        }
      }
    });
  } catch (error) {
    console.error('خطأ في جلب معلومات النظام:', error);
    res.status(500).json({ 
      error: 'فشل في جلب معلومات النظام: ' + error.message 
    });
  }
});

// فحص صحة النظام
router.get('/health', async (req, res) => {
  try {
    const dbConnected = await testConnection();
    const memoryUsage = process.memoryUsage();
    const uptime = process.uptime();
    
    const health = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: uptime,
      database: dbConnected ? 'connected' : 'disconnected',
      memory: {
        used: Math.round(memoryUsage.heapUsed / 1024 / 1024) + ' MB',
        total: Math.round(memoryUsage.heapTotal / 1024 / 1024) + ' MB'
      }
    };
    
    if (!dbConnected) {
      health.status = 'unhealthy';
      return res.status(503).json(health);
    }
    
    res.json(health);
  } catch (error) {
    console.error('خطأ في فحص صحة النظام:', error);
    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error.message
    });
  }
});

module.exports = router;