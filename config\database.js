const mysql = require("mysql2");
require("dotenv").config();

// إعدادات قاعدة البيانات الافتراضية
let dbConfig = {
  host: "localhost",
  user: "root",
  password: "Hbkhbkhbk@123",
  database: "hassan",
  port: 3306,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
};

// إنشاء connection pool
let pool = mysql.createPool(dbConfig);

// التحقف من الاتصال
pool.getConnection((err, connection) => {
  if (err) {
    console.error("فشل الاتصال بقاعدة البيانات:", err);
  } else {
    connection.release();
  }
});

// دالة لتحديث إعدادات قاعدة البيانات
const updateDatabaseConfig = async (newConfig) => {
  // إغلاق pool الحالي
  await pool.end();
  
  // تحديث الإعدادات
  dbConfig = {
    ...dbConfig,
    ...newConfig
  };
  
  // إنشاء pool جديد
  pool = mysql.createPool(dbConfig);
  
  return pool;
};

// دالة للحصول على الإعدادات الحالية
const getCurrentConfig = () => {
  return {
    host: dbConfig.host,
    port: dbConfig.port,
    database: dbConfig.database,
    username: dbConfig.user
  };
};

// دالة للتحقق من الاتصال
const checkConnection = async () => {
  try {
    const connection = await pool.promise().getConnection();
    await connection.ping();
    connection.release();
    return true;
  } catch (error) {
    console.error('خطأ في الاتصال بقاعدة البيانات:', error);
    return false;
  }
};

// دالة لاختبار الاتصال
const testConnection = async () => {
  try {
    const connection = await pool.promise().getConnection();
    await connection.ping();
    connection.release();
    return true;
  } catch (error) {
    return false;
  }
};

module.exports = {
  pool,
  dbConfig,
  updateDatabaseConfig,
  getCurrentConfig,
  checkConnection,
  testConnection
};